package main

import (
	"fmt"
	"html/template"
	"log"
	"net/http"
	"strconv"
	"time"
)

type Counter struct {
	Value int
}

type TodoItem struct {
	ID   int
	Text string
	Done bool
}

var counter = Counter{Value: 0}
var todos = []TodoItem{
	{ID: 1, Text: "Learn HTMX", Done: false},
	{ID: 2, Text: "Build a Go app", Done: true},
}
var nextTodoID = 3

func main() {
	// GET routes - for retrieving data
	http.HandleFunc("GET /", homeHandler)
	http.HandleFunc("GET /counter", counterHandler)
	http.HandleFunc("GET /todos", todosHandler)
	http.HandleFunc("GET /time", timeHandler)
	http.HandleFunc("GET /search", searchHandler)
	http.HandleFunc("GET /account/display", accountDisplayHandler)
	http.HandleFunc("GET /user-preferences", userPreferencesHandler)
	http.HandleFunc("POST /user-preferences", userPreferencesPostHandler)
	http.HandleFunc("POST /analytics", analyticsHandler)

	// POST routes - for modifying data
	http.HandleFunc("POST /counter/increment", incrementHandler)
	http.HandleFunc("POST /counter/decrement", decrementHandler)
	http.HandleFunc("POST /todos", addTodoHandler)
	http.HandleFunc("POST /todos/toggle/{id}", toggleTodoHandler)
	http.HandleFunc("POST /account/enable", accountEnableHandler)

	// DELETE routes - for removing data
	http.HandleFunc("DELETE /todos/{id}", deleteTodoHandler)

	log.Fatal(http.ListenAndServe(":8080", nil))
}

func homeHandler(w http.ResponseWriter, r *http.Request) {
	tmpl := `<!DOCTYPE html>
<html>
<head>
    <title>HTMX Go Test App</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .counter { font-size: 24px; font-weight: bold; color: #333; }
        .todo-item { padding: 10px; margin: 5px 0; border: 1px solid #eee; border-radius: 3px; }
        .todo-done { text-decoration: line-through; color: #888; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        input[type="text"] { padding: 8px; margin: 5px; width: 200px; }
        .loading { opacity: 0.5; }
    </style>
</head>
<body>
    <h1>HTMX Go Test App</h1>
    
	<button id="enable-account" hx-post="/account/enable" hx-target="#account-enabled">
  		Enable Your Account
	</button>
	<div id="account-enabled"></div>
	
	<button id="show-account" hx-get="/account/display" 
		hx-target="#account-display"
		hx-swap="outerHTML"
		hx-trigger="click from:#show-account, click from:#enable-account"
		>
		Show account
	</button>
	<div id="account-display"></div>

	<div>
    <div class="section">
        <h2>Counter Example debug</h2>
        <div id="counter" hx-get="/counter" hx-trigger="load">
            Loading counter...
        </div>
        <button hx-post="/counter/increment" hx-target="#counter">+</button>
        <button hx-post="/counter/decrement" hx-target="#counter">-</button>
    </div>

    <div class="section">
        <h2>Todo List Example</h2>
        <form hx-post="/todos" hx-target="#todos" hx-swap="outerHTML">
            <input type="text" name="todo" placeholder="Enter a new todo" required>
            <button type="submit">Add Todo</button>
        </form>
        <div id="todos" hx-get="/todos" hx-trigger="load">
            Loading todos...
        </div>
    </div>

    <div class="section">
        <h2>Live Time Example</h2>
        <div id="time" hx-get="/time" hx-trigger="every 1s">
            Loading time...
        </div>
    </div>

    <div class="section">
        <h2>Search Example</h2>
        <input type="text"
               hx-get="/search"
               hx-target="#search-results"
               hx-trigger="keyup changed delay:300ms"
               placeholder="Type to search...">
        <div id="search-results"></div>
    </div>

    <div class="section">
        <h2>Data Passing: GET vs POST Comparison</h2>
        <p>This demonstrates how data is passed differently in hx-get vs hx-post requests.</p>

        <!-- Hidden input - perfect for IDs and non-editable data -->
        <input type="hidden" id="user-id" name="userId" value="12345">

        <div style="margin-bottom: 15px;">
            <label>User Email:</label>
            <input name="email" value="<EMAIL>" style="margin-left: 10px;">
        </div>

        <div style="margin-bottom: 15px;">
            <label>User Role:</label>
            <input name="role" value="admin" style="margin-left: 10px;">
        </div>

        <!-- This div has data but won't be included by hx-include -->
        <div id="user-metadata" data-department="engineering" data-level="senior"
             style="padding: 8px; background: #f0f0f0; border-radius: 3px; margin-bottom: 15px; font-size: 12px;">
            📝 This div has data-department="engineering" and data-level="senior" but won't be included by hx-include
        </div>

        <!-- These form elements WILL be included -->
        <div style="margin-bottom: 15px;">
            <label>Department:</label>
            <select id="department-select" name="department" style="margin-left: 10px;">
                <option value="engineering" selected>Engineering</option>
                <option value="marketing">Marketing</option>
                <option value="sales">Sales</option>
            </select>
        </div>

        <div style="margin-bottom: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
            <h4>🔍 GET Request Example (hx-get)</h4>
            <p>Data will be sent as <strong>URL query parameters</strong></p>
            <select name="theme-get" hx-get="/user-preferences"
                    hx-include="[name='email'], [name='role'], #user-id, #department-select"
                    hx-target="#get-result"
                    hx-trigger="change">
                <option value="">Select theme for GET...</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
            </select>
            <div id="get-result" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px;">
                <em>Select a theme to see GET request data passing</em>
            </div>
        </div>

        <div style="margin-bottom: 20px; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px;">
            <h4>📝 POST Request Example (hx-post)</h4>
            <p>Data will be sent in the <strong>request body</strong> as form data</p>
            <select name="theme-post" hx-post="/user-preferences"
                    hx-include="[name='email'], [name='role'], #user-id, #department-select"
                    hx-target="#post-result"
                    hx-trigger="change">
                <option value="">Select theme for POST...</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
            </select>
            <div id="post-result" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px;">
                <em>Select a theme to see POST request data passing</em>
            </div>
        </div>

        <div style="padding: 15px; background: #e8f4f8; border-radius: 5px;">
            <h4>📊 Key Differences:</h4>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background: #f1f3f4;">
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Aspect</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">hx-get</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">hx-post</th>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Data Location</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd;">URL query parameters</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">Request body</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Visibility</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd;">Visible in URL/browser history</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">Hidden from URL</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Size Limit</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd;">Limited by URL length (~2KB)</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">Much larger (server dependent)</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Caching</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd;">Can be cached by browsers</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">Not cached</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Server Access</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd;">r.URL.Query().Get("param")</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">r.FormValue("param") or r.PostFormValue("param")</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="section">
        <h2>HX-Vals Example</h2>
        <p>The <code>hx-vals</code> attribute lets you add extra data to requests in JSON format.</p>

        <div style="margin-bottom: 15px;">
            <h4>Static hx-vals:</h4>
            <button hx-post="/analytics"
                    hx-vals='{"action": "button_click", "page": "demo", "version": "1.0"}'
                    hx-target="#analytics-result">
                Track Button Click (Static Values)
            </button>
        </div>

        <div style="margin-bottom: 15px;">
            <h4>Dynamic hx-vals with JavaScript:</h4>
            <button hx-post="/analytics"
                    hx-vals='js:{"action": "dynamic_click", "timestamp": Date.now(), "random": Math.floor(Math.random() * 1000)}'
                    hx-target="#analytics-result">
                Track Button Click (Dynamic Values)
            </button>
        </div>

        <div style="margin-bottom: 15px;">
            <h4>Combining Form Data + hx-vals + hx-include:</h4>
            <form style="display: inline-block; margin-right: 10px;">
                <input name="username" placeholder="Enter username" value="john_doe" style="margin-right: 5px;">
                <button type="button"
                        hx-post="/analytics"
                        hx-vals='{"action": "form_submit", "source": "demo_form"}'
                        hx-include="[name='username'], [name='email']"
                        hx-target="#analytics-result">
                    Submit with All Data Types
                </button>
            </form>
        </div>

        <div id="analytics-result" style="padding: 10px; background: #f8f9fa; border-radius: 3px; margin-bottom: 15px;">
            <em>Click any button above to see how different data passing methods work together</em>
        </div>

        <div style="padding: 15px; background: #e8f4f8; border-radius: 5px;">
            <h4>📋 All Data Passing Methods:</h4>
            <ol>
                <li><strong>Form Values:</strong> Automatically included from form inputs</li>
                <li><strong>hx-vals:</strong> Static JSON or dynamic JavaScript expressions</li>
                <li><strong>hx-include:</strong> Values from other elements (CSS selector)</li>
                <li><strong>Path Parameters:</strong> From URL patterns like <code>/users/{id}</code></li>
                <li><strong>Query Parameters:</strong> Direct URL parameters</li>
            </ol>

            <h4>🔧 hx-vals Syntax:</h4>
            <ul>
                <li><code>hx-vals='{"key": "value"}'</code> - Static JSON</li>
                <li><code>hx-vals='js:{"key": expression}'</code> - Dynamic JavaScript</li>
                <li>Values are merged with form data and hx-include data</li>
                <li>hx-vals can override form values if keys match</li>
            </ul>
        </div>
    </div>
</body>
</html>`

	t, _ := template.New("home").Parse(tmpl)
	t.Execute(w, nil)
}

func accountDisplayHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Fprint(w, "<div>Account Display</div>")
}

func accountEnableHandler(w http.ResponseWriter, r *http.Request) {
	//fmt.Fprint(w, "<div>Account Enabled Successfully!</div>")
	fmt.Fprint(w, "beep")
}

func counterHandler(w http.ResponseWriter, r *http.Request) {
	tmpl := `<div class="counter">Count: {{.Value}}</div>`
	t, _ := template.New("counter").Parse(tmpl)
	t.Execute(w, counter)
}

func incrementHandler(w http.ResponseWriter, r *http.Request) {
	counter.Value++
	counterHandler(w, r)
}

func decrementHandler(w http.ResponseWriter, r *http.Request) {
	counter.Value--
	counterHandler(w, r)
}

func todosHandler(w http.ResponseWriter, r *http.Request) {
	tmpl := `<div id="todos">
{{range .}}
    <div class="todo-item {{if .Done}}todo-done{{end}}">
        <input type="checkbox" {{if .Done}}checked{{end}}
               hx-post="/todos/toggle/{{.ID}}"
               hx-target="#todos"
               hx-swap="outerHTML">
        {{.Text}}
        <button hx-delete="/todos/{{.ID}}"
                hx-target="#todos"
                hx-swap="outerHTML"
                hx-confirm="Are you sure?">Delete</button>
    </div>
{{end}}
</div>`
	t, _ := template.New("todos").Parse(tmpl)
	t.Execute(w, todos)
}

func addTodoHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		todoText := r.FormValue("todo")
		if todoText != "" {
			todos = append(todos, TodoItem{
				ID:   nextTodoID,
				Text: todoText,
				Done: false,
			})
			nextTodoID++
		}
	}
	todosHandler(w, r)
}

func toggleTodoHandler(w http.ResponseWriter, r *http.Request) {
	idStr := r.PathValue("id")
	id, err := strconv.Atoi(idStr)
	if err == nil {
		for i := range todos {
			if todos[i].ID == id {
				todos[i].Done = !todos[i].Done
				break
			}
		}
	}
	todosHandler(w, r)
}

func deleteTodoHandler(w http.ResponseWriter, r *http.Request) {
	idStr := r.PathValue("id")
	id, err := strconv.Atoi(idStr)
	if err == nil {
		for i, todo := range todos {
			if todo.ID == id {
				todos = append(todos[:i], todos[i+1:]...)
				break
			}
		}
	}
	todosHandler(w, r)
}

func timeHandler(w http.ResponseWriter, r *http.Request) {
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	fmt.Fprintf(w, "<div>Current time: %s</div>", currentTime)
}

func searchHandler(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query().Get("q")
	if query == "" {
		fmt.Fprint(w, "<div>Start typing to search...</div>")
		return
	}

	// Simulate search results
	results := []string{
		"Apple", "Banana", "Cherry", "Date", "Elderberry",
		"Fig", "Grape", "Honeydew", "Kiwi", "Lemon",
	}

	var matches []string
	for _, result := range results {
		if len(query) > 0 && contains(result, query) {
			matches = append(matches, result)
		}
	}

	if len(matches) == 0 {
		fmt.Fprintf(w, "<div>No results found for '%s'</div>", query)
		return
	}

	fmt.Fprint(w, "<div><strong>Search Results:</strong><ul>")
	for _, match := range matches {
		fmt.Fprintf(w, "<li>%s</li>", match)
	}
	fmt.Fprint(w, "</ul></div>")
}

func userPreferencesHandler(w http.ResponseWriter, r *http.Request) {
	// Debug: Print full request details
	fmt.Printf("\n=== REQUEST DEBUG ===\n")
	fmt.Printf("Method: %s\n", r.Method)
	fmt.Printf("URL: %s\n", r.URL.String())
	fmt.Printf("Query Parameters: %v\n", r.URL.Query())

	// Print all headers
	fmt.Printf("Headers:\n")
	for name, values := range r.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", name, value)
		}
	}

	// Parse form data (this reads the body for POST requests)
	r.ParseForm()
	fmt.Printf("Form Values: %v\n", r.Form)
	fmt.Printf("PostForm Values: %v\n", r.PostForm)

	// Print raw body content (note: body can only be read once, so we'll read it here)
	if r.Body != nil {
		bodyBytes := make([]byte, 1024) // Read up to 1KB
		n, _ := r.Body.Read(bodyBytes)
		if n > 0 {
			fmt.Printf("Body Content: %s\n", string(bodyBytes[:n]))
		} else {
			fmt.Printf("Body Content: (empty or already read)\n")
		}
	}
	fmt.Printf("==================\n\n")

	// Extract values from the request - for GET, all data comes from URL query parameters
	email := r.URL.Query().Get("email")
	role := r.URL.Query().Get("role")
	theme := r.URL.Query().Get("theme-get")       // Note: different name for GET example
	userId := r.URL.Query().Get("userId")         // From hidden input #user-id
	department := r.URL.Query().Get("department") // From select #department-select

	if theme == "" {
		fmt.Fprint(w, "<em>Select a theme to see the preferences...</em>")
		return
	}

	// Simulate different responses based on the included values
	response := fmt.Sprintf(`
		<div style="padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">
			<h4>GET Preferences Updated!</h4>
			<p><strong>User ID:</strong> %s <em>(from hidden input #user-id)</em></p>
			<p><strong>Email:</strong> %s <em>(from input[name='email'])</em></p>
			<p><strong>Role:</strong> %s <em>(from input[name='role'])</em></p>
			<p><strong>Department:</strong> %s <em>(from select #department-select)</em></p>
			<p><strong>Theme:</strong> %s <em>(from current select)</em></p>
			<hr>
			<p><em>🔍 This data was sent as <strong>URL query parameters</strong>!</em></p>
			<p><small>Notice how hx-include="#user-id" included the hidden input's value, but #user-metadata div was ignored.</small></p>
		</div>
	`, userId, email, role, department, theme)

	fmt.Fprint(w, response)
}

func userPreferencesPostHandler(w http.ResponseWriter, r *http.Request) {
	// Debug: Print full request details for POST
	fmt.Printf("\n=== POST REQUEST DEBUG ===\n")
	fmt.Printf("Method: %s\n", r.Method)
	fmt.Printf("URL: %s\n", r.URL.String())
	fmt.Printf("Query Parameters: %v\n", r.URL.Query())

	// Print all headers
	fmt.Printf("Headers:\n")
	for name, values := range r.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", name, value)
		}
	}

	// Parse form data (this reads the body for POST requests)
	r.ParseForm()
	fmt.Printf("Form Values (combined): %v\n", r.Form)
	fmt.Printf("PostForm Values (body only): %v\n", r.PostForm)
	fmt.Printf("========================\n\n")

	// Extract values from the request - for POST, use FormValue which checks both URL and body
	email := r.FormValue("email")
	role := r.FormValue("role")
	theme := r.FormValue("theme-post")      // Note: different name for POST example
	userId := r.FormValue("userId")         // From hidden input #user-id
	department := r.FormValue("department") // From select #department-select

	if theme == "" {
		fmt.Fprint(w, "<em>Select a theme to see the POST preferences...</em>")
		return
	}

	// Simulate different responses based on the included values
	response := fmt.Sprintf(`
		<div style="padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 3px;">
			<h4>POST Preferences Updated!</h4>
			<p><strong>Email:</strong> %s</p>
			<p><strong>Role:</strong> %s</p>
			<p><strong>Theme:</strong> %s</p>
			<hr>
			<p><em>📝 This data was sent in the <strong>request body</strong>, not the URL!</em></p>
			<p><small>Check the server console to see the difference in how the data was transmitted.</small></p>
		</div>
	`, email, role, theme)

	fmt.Fprint(w, response)
}

func analyticsHandler(w http.ResponseWriter, r *http.Request) {
	// Debug: Print all the different ways data can be received
	fmt.Printf("\n=== ANALYTICS REQUEST DEBUG ===\n")
	fmt.Printf("Method: %s\n", r.Method)
	fmt.Printf("URL: %s\n", r.URL.String())

	// Parse form data
	r.ParseForm()

	fmt.Printf("\n📊 Data Sources:\n")
	fmt.Printf("1. Query Parameters: %v\n", r.URL.Query())
	fmt.Printf("2. Form Values (combined): %v\n", r.Form)
	fmt.Printf("3. PostForm Values (body only): %v\n", r.PostForm)

	// Show individual values
	fmt.Printf("\n🔍 Individual Values:\n")
	fmt.Printf("  action: %s\n", r.FormValue("action"))
	fmt.Printf("  page: %s\n", r.FormValue("page"))
	fmt.Printf("  version: %s\n", r.FormValue("version"))
	fmt.Printf("  timestamp: %s\n", r.FormValue("timestamp"))
	fmt.Printf("  random: %s\n", r.FormValue("random"))
	fmt.Printf("  username: %s\n", r.FormValue("username"))
	fmt.Printf("  email: %s\n", r.FormValue("email"))
	fmt.Printf("  source: %s\n", r.FormValue("source"))
	fmt.Printf("===============================\n\n")

	// Build response showing all received data
	response := `<div style="padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
		<h4>📊 Analytics Data Received!</h4>
		<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
			<tr style="background: #f8f9fa;">
				<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Parameter</th>
				<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Value</th>
				<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Source</th>
			</tr>`

	// Add rows for each parameter we might receive
	params := map[string]string{
		"action":    "hx-vals",
		"page":      "hx-vals (static)",
		"version":   "hx-vals (static)",
		"timestamp": "hx-vals (dynamic js:)",
		"random":    "hx-vals (dynamic js:)",
		"username":  "form input",
		"email":     "hx-include",
		"source":    "hx-vals",
	}

	for param, source := range params {
		value := r.FormValue(param)
		if value != "" {
			response += fmt.Sprintf(`
			<tr>
				<td style="padding: 8px; border: 1px solid #ddd;"><code>%s</code></td>
				<td style="padding: 8px; border: 1px solid #ddd;">%s</td>
				<td style="padding: 8px; border: 1px solid #ddd;"><em>%s</em></td>
			</tr>`, param, value, source)
		}
	}

	response += `
		</table>
		<hr style="margin: 15px 0;">
		<p><strong>💡 Key Points:</strong></p>
		<ul>
			<li>All data sources are merged into the same request</li>
			<li>Server accesses everything via <code>r.FormValue("key")</code></li>
			<li>hx-vals can override form values if keys match</li>
			<li>Dynamic hx-vals (js:) are evaluated when the request is made</li>
		</ul>
	</div>`

	fmt.Fprint(w, response)
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			s[:len(substr)] == substr ||
			s[len(s)-len(substr):] == substr ||
			(len(s) > len(substr) &&
				func() bool {
					for i := 1; i <= len(s)-len(substr); i++ {
						if s[i:i+len(substr)] == substr {
							return true
						}
					}
					return false
				}()))
}
