package main

import (
	"fmt"
	"html/template"
	"log"
	"net/http"
	"strconv"
	"time"
)

type Counter struct {
	Value int
}

type TodoItem struct {
	ID   int
	Text string
	Done bool
}

var counter = Counter{Value: 0}
var todos = []TodoItem{
	{ID: 1, Text: "Learn HTMX", Done: false},
	{ID: 2, Text: "Build a Go app", Done: true},
}
var nextTodoID = 3

func main() {
	// GET routes - for retrieving data
	http.HandleFunc("GET /", homeHandler)
	http.HandleFunc("GET /counter", counterHandler)
	http.HandleFunc("GET /todos", todosHandler)
	http.HandleFunc("GET /time", timeHandler)
	http.HandleFunc("GET /search", searchHandler)
	http.HandleFunc("GET /account/display", accountDisplayHandler)
	http.HandleFunc("GET /user-preferences", userPreferencesHandler)

	// POST routes - for modifying data
	http.HandleFunc("POST /counter/increment", incrementHandler)
	http.HandleFunc("POST /counter/decrement", decrementHandler)
	http.HandleFunc("POST /todos", addTodoHandler)
	http.HandleFunc("POST /todos/toggle/{id}", toggleTodoHandler)
	http.HandleFunc("POST /account/enable", accountEnableHandler)

	// DELETE routes - for removing data
	http.HandleFunc("DELETE /todos/{id}", deleteTodoHandler)

	log.Fatal(http.ListenAndServe(":8080", nil))
}

func homeHandler(w http.ResponseWriter, r *http.Request) {
	tmpl := `<!DOCTYPE html>
<html>
<head>
    <title>HTMX Go Test App</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .counter { font-size: 24px; font-weight: bold; color: #333; }
        .todo-item { padding: 10px; margin: 5px 0; border: 1px solid #eee; border-radius: 3px; }
        .todo-done { text-decoration: line-through; color: #888; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        input[type="text"] { padding: 8px; margin: 5px; width: 200px; }
        .loading { opacity: 0.5; }
    </style>
</head>
<body>
    <h1>HTMX Go Test App</h1>
    
	<button id="enable-account" hx-post="/account/enable" hx-target="#account-enabled">
  		Enable Your Account
	</button>
	<div id="account-enabled"></div>
	
	<button id="show-account" hx-get="/account/display" 
		hx-target="#account-display"
		hx-swap="outerHTML"
		hx-trigger="click from:#show-account, click from:#enable-account"
		>
		Show account
	</button>
	<div id="account-display"></div>

	<div>
    <div class="section">
        <h2>Counter Example debug</h2>
        <div id="counter" hx-get="/counter" hx-trigger="load">
            Loading counter...
        </div>
        <button hx-post="/counter/increment" hx-target="#counter">+</button>
        <button hx-post="/counter/decrement" hx-target="#counter">-</button>
    </div>

    <div class="section">
        <h2>Todo List Example</h2>
        <form hx-post="/todos" hx-target="#todos" hx-swap="outerHTML">
            <input type="text" name="todo" placeholder="Enter a new todo" required>
            <button type="submit">Add Todo</button>
        </form>
        <div id="todos" hx-get="/todos" hx-trigger="load">
            Loading todos...
        </div>
    </div>

    <div class="section">
        <h2>Live Time Example</h2>
        <div id="time" hx-get="/time" hx-trigger="every 1s">
            Loading time...
        </div>
    </div>

    <div class="section">
        <h2>Search Example</h2>
        <input type="text"
               hx-get="/search"
               hx-target="#search-results"
               hx-trigger="keyup changed delay:300ms"
               placeholder="Type to search...">
        <div id="search-results"></div>
    </div>

    <div class="section">
        <h2>HX-Include Example</h2>
        <p>This demonstrates how hx-include allows one element to include values from other elements in its request.</p>

        <div style="margin-bottom: 15px;">
            <label>User Email:</label>
            <input name="email" value="<EMAIL>" style="margin-left: 10px;">
        </div>

        <div style="margin-bottom: 15px;">
            <label>User Role:</label>
            <input name="role" value="admin" style="margin-left: 10px;">
        </div>

        <div style="margin-bottom: 15px;">
            <label>Theme Preference:</label>
            <select name="theme" hx-get="/user-preferences"
                    hx-include="[name='email'], [name='role']"
                    hx-target="#preferences-result"
                    hx-trigger="change">
                <option value="">Select theme...</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
            </select>
        </div>

        <div id="preferences-result" style="padding: 10px; background: #f5f5f5; border-radius: 3px;">
            <em>Select a theme to see how hx-include works. The request will include the email and role values above.</em>
        </div>

        <div style="margin-top: 15px; padding: 10px; background: #e8f4f8; border-radius: 3px;">
            <strong>How it works:</strong><br>
            • The select element uses <code>hx-include="[name='email'], [name='role']"</code><br>
            • When you change the theme, HTMX will include the email and role input values in the GET request<br>
            • The server can then use all three values (theme, email, role) to provide personalized preferences
        </div>
    </div>
</body>
</html>`

	t, _ := template.New("home").Parse(tmpl)
	t.Execute(w, nil)
}

func accountDisplayHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Fprint(w, "<div>Account Display</div>")
}

func accountEnableHandler(w http.ResponseWriter, r *http.Request) {
	//fmt.Fprint(w, "<div>Account Enabled Successfully!</div>")
	fmt.Fprint(w, "beep")
}

func counterHandler(w http.ResponseWriter, r *http.Request) {
	tmpl := `<div class="counter">Count: {{.Value}}</div>`
	t, _ := template.New("counter").Parse(tmpl)
	t.Execute(w, counter)
}

func incrementHandler(w http.ResponseWriter, r *http.Request) {
	counter.Value++
	counterHandler(w, r)
}

func decrementHandler(w http.ResponseWriter, r *http.Request) {
	counter.Value--
	counterHandler(w, r)
}

func todosHandler(w http.ResponseWriter, r *http.Request) {
	tmpl := `<div id="todos">
{{range .}}
    <div class="todo-item {{if .Done}}todo-done{{end}}">
        <input type="checkbox" {{if .Done}}checked{{end}}
               hx-post="/todos/toggle/{{.ID}}"
               hx-target="#todos"
               hx-swap="outerHTML">
        {{.Text}}
        <button hx-delete="/todos/{{.ID}}"
                hx-target="#todos"
                hx-swap="outerHTML"
                hx-confirm="Are you sure?">Delete</button>
    </div>
{{end}}
</div>`
	t, _ := template.New("todos").Parse(tmpl)
	t.Execute(w, todos)
}

func addTodoHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		todoText := r.FormValue("todo")
		if todoText != "" {
			todos = append(todos, TodoItem{
				ID:   nextTodoID,
				Text: todoText,
				Done: false,
			})
			nextTodoID++
		}
	}
	todosHandler(w, r)
}

func toggleTodoHandler(w http.ResponseWriter, r *http.Request) {
	idStr := r.PathValue("id")
	id, err := strconv.Atoi(idStr)
	if err == nil {
		for i := range todos {
			if todos[i].ID == id {
				todos[i].Done = !todos[i].Done
				break
			}
		}
	}
	todosHandler(w, r)
}

func deleteTodoHandler(w http.ResponseWriter, r *http.Request) {
	idStr := r.PathValue("id")
	id, err := strconv.Atoi(idStr)
	if err == nil {
		for i, todo := range todos {
			if todo.ID == id {
				todos = append(todos[:i], todos[i+1:]...)
				break
			}
		}
	}
	todosHandler(w, r)
}

func timeHandler(w http.ResponseWriter, r *http.Request) {
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	fmt.Fprintf(w, "<div>Current time: %s</div>", currentTime)
}

func searchHandler(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query().Get("q")
	if query == "" {
		fmt.Fprint(w, "<div>Start typing to search...</div>")
		return
	}

	// Simulate search results
	results := []string{
		"Apple", "Banana", "Cherry", "Date", "Elderberry",
		"Fig", "Grape", "Honeydew", "Kiwi", "Lemon",
	}

	var matches []string
	for _, result := range results {
		if len(query) > 0 && contains(result, query) {
			matches = append(matches, result)
		}
	}

	if len(matches) == 0 {
		fmt.Fprintf(w, "<div>No results found for '%s'</div>", query)
		return
	}

	fmt.Fprint(w, "<div><strong>Search Results:</strong><ul>")
	for _, match := range matches {
		fmt.Fprintf(w, "<li>%s</li>", match)
	}
	fmt.Fprint(w, "</ul></div>")
}

func userPreferencesHandler(w http.ResponseWriter, r *http.Request) {
	// Extract values from the request
	fmt.
	email := r.URL.Query().Get("email")
	role := r.URL.Query().Get("role")
	theme := r.URL.Query().Get("theme")

	if theme == "" {
		fmt.Fprint(w, "<em>Select a theme to see the preferences...</em>")
		return
	}

	// Simulate different responses based on the included values
	response := fmt.Sprintf(`
		<div style="padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 3px;">
			<h4>Preferences Updated!</h4>
			<p><strong>Email:</strong> %s</p>
			<p><strong>Role:</strong> %s</p>
			<p><strong>Theme:</strong> %s</p>
			<hr>
			<p><em>Notice how the server received all three values even though only the theme dropdown was changed.
			This is the power of hx-include!</em></p>
		</div>
	`, email, role, theme)

	fmt.Fprint(w, response)
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			s[:len(substr)] == substr ||
			s[len(s)-len(substr):] == substr ||
			(len(s) > len(substr) &&
				func() bool {
					for i := 1; i <= len(s)-len(substr); i++ {
						if s[i:i+len(substr)] == substr {
							return true
						}
					}
					return false
				}()))
}
